# coding=utf-8
"""
瀚海Agent API客户端测试
"""

import sys
import os

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from hanhai_client_api import (
    HanHaiClient, 
    ResponseMode, 
    HanHaiClientError,
    create_client
)


def test_basic_functionality():
    """测试基本功能"""
    # 注意：这里需要替换为实际的API地址和密钥
    host = "https://your-api-host.com"
    api_key = "your-api-key"
    
    try:
        # 创建客户端
        client = create_client(host, api_key)
        print("✓ 客户端创建成功")
        
        # 测试获取应用配置
        try:
            config = client.get_parameters(user="test_user")
            print("✓ 获取应用配置成功")
        except HanHaiClientError as e:
            print(f"✗ 获取应用配置失败: {e}")
        
        # 测试获取开场白
        try:
            opening = client.get_opening_statement()
            print("✓ 获取开场白成功")
        except HanHaiClientError as e:
            print(f"✗ 获取开场白失败: {e}")
        
        # 测试发送阻塞消息
        try:
            message = client.send_message(
                query="你好",
                user="test_user",
                response_mode=ResponseMode.BLOCKING
            )
            print(f"✓ 发送阻塞消息成功: {message.answer[:50]}...")
            
            # 测试获取会话列表
            conversations = client.get_conversations(user="test_user")
            print(f"✓ 获取会话列表成功，共{len(conversations['data'])}个会话")
            
            # 测试获取消息历史
            messages = client.get_messages(
                conversation_id=message.conversation_id,
                user="test_user"
            )
            print(f"✓ 获取消息历史成功，共{len(messages['data'])}条消息")
            
        except HanHaiClientError as e:
            print(f"✗ 消息相关操作失败: {e}")
        
        # 测试流式消息
        try:
            print("测试流式消息:")
            for event in client.send_message(
                query="请简单介绍一下你自己",
                user="test_user",
                response_mode=ResponseMode.STREAMING
            ):
                if event.event == 'message':
                    answer = event.data.get('answer', '')
                    if answer:
                        print(answer, end='', flush=True)
                elif event.event == 'message_end':
                    print("\n✓ 流式消息测试完成")
                    break
        except HanHaiClientError as e:
            print(f"✗ 流式消息测试失败: {e}")
        
        client.close()
        print("✓ 客户端关闭成功")
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")


def test_context_manager():
    """测试上下文管理器"""
    host = "https://your-api-host.com"
    api_key = "your-api-key"
    
    try:
        with create_client(host, api_key) as client:
            print("✓ 上下文管理器测试开始")
            # 在这里可以进行各种操作
            config = client.get_parameters(user="test_user")
            print("✓ 在上下文管理器中成功调用API")
        print("✓ 上下文管理器自动关闭客户端")
    except Exception as e:
        print(f"✗ 上下文管理器测试失败: {e}")


def test_error_handling():
    """测试错误处理"""
    # 使用无效的API地址和密钥进行测试
    client = create_client("https://invalid-host.com", "invalid-key")
    
    try:
        client.send_message(
            query="测试",
            user="test_user",
            response_mode=ResponseMode.BLOCKING
        )
    except HanHaiClientError as e:
        print(f"✓ 错误处理正常: {e}")
    except Exception as e:
        print(f"✗ 未预期的错误类型: {e}")
    finally:
        client.close()


if __name__ == '__main__':
    print("=== 瀚海Agent API客户端测试 ===\n")
    
    print("1. 基本功能测试:")
    test_basic_functionality()
    
    print("\n2. 上下文管理器测试:")
    test_context_manager()
    
    print("\n3. 错误处理测试:")
    test_error_handling()
    
    print("\n=== 测试完成 ===")
    print("\n注意：要运行实际测试，请在代码中替换为真实的API地址和密钥")
