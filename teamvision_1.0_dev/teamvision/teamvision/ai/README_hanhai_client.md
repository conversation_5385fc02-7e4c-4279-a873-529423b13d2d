# 瀚海Agent API客户端

这是一个用于与瀚海Agent API交互的Python客户端库，支持对话消息发送、会话管理、消息历史查询等功能。

## 功能特性

- ✅ 支持阻塞和流式两种响应模式
- ✅ 完整的会话管理功能
- ✅ 消息历史查询
- ✅ 应用配置获取
- ✅ 建议问题获取
- ✅ 错误处理和异常管理
- ✅ 上下文管理器支持
- ✅ 类型提示支持

## 安装依赖

```bash
pip install requests
```

## 快速开始

### 基本使用

```python
from hanhai_client_api import create_client, ResponseMode

# 创建客户端
client = create_client('https://your-api-host.com', 'your-api-key')

try:
    # 发送阻塞消息
    message = client.send_message(
        query="你好",
        user="user123",
        response_mode=ResponseMode.BLOCKING
    )
    print(f"回答: {message.answer}")
    
    # 发送流式消息
    for event in client.send_message(
        query="请介绍一下你自己",
        user="user123",
        response_mode=ResponseMode.STREAMING,
        conversation_id=message.conversation_id
    ):
        if event.event == 'message':
            print(event.data.get('answer', ''), end='', flush=True)
        elif event.event == 'message_end':
            print("\n消息结束")
            break

finally:
    client.close()
```

### 使用上下文管理器

```python
from hanhai_client_api import create_client, ResponseMode

with create_client('https://your-api-host.com', 'your-api-key') as client:
    message = client.send_message(
        query="你好",
        user="user123",
        response_mode=ResponseMode.BLOCKING
    )
    print(f"回答: {message.answer}")
# 客户端会自动关闭
```

## API参考

### 主要类

#### HanHaiClient

主要的客户端类，提供所有API功能。

**初始化参数:**
- `host`: API服务地址
- `api_key`: API密钥
- `timeout`: 请求超时时间（秒），默认30

#### ResponseMode

响应模式枚举:
- `BLOCKING`: 阻塞模式
- `STREAMING`: 流式模式

#### ChatMessage

聊天消息数据类:
- `id`: 消息ID
- `conversation_id`: 会话ID
- `query`: 用户输入
- `answer`: AI回答
- `reason`: 推理内容（可选）
- `created_at`: 创建时间戳
- `retriever_resources`: 检索资源列表（可选）

### 主要方法

#### send_message()

发送对话消息

```python
message = client.send_message(
    query="你的问题",
    user="用户标识",
    response_mode=ResponseMode.BLOCKING,  # 或 STREAMING
    conversation_id="会话ID（可选）",
    inputs={"key": "value"}  # 输入参数（可选）
)
```

#### get_conversations()

获取会话列表

```python
result = client.get_conversations(
    user="用户标识",
    last_id="最后一条记录ID（可选）",
    limit=20  # 返回条数
)
```

#### get_messages()

获取会话历史消息

```python
result = client.get_messages(
    conversation_id="会话ID",
    user="用户标识",
    first_id="第一条记录ID（可选）",
    limit=20  # 返回条数
)
```

#### delete_conversation()

删除会话

```python
success = client.delete_conversation(
    conversation_id="会话ID",
    user="用户标识"
)
```

#### get_parameters()

获取应用配置信息

```python
config = client.get_parameters(user="用户标识")
```

#### get_suggested_questions()

获取消息建议问题

```python
questions = client.get_suggested_questions(
    message_id="消息ID",
    user="用户标识"
)
```

#### get_opening_statement()

获取应用开场白

```python
opening = client.get_opening_statement()
```

## 错误处理

客户端使用 `HanHaiClientError` 异常来处理API错误:

```python
from hanhai_client_api import HanHaiClientError

try:
    message = client.send_message(query="测试", user="user123")
except HanHaiClientError as e:
    print(f"API错误 [{e.status_code}] {e.code}: {e.message}")
```

## 流式响应处理

流式模式返回事件迭代器，主要事件类型:

- `message`: 消息内容块
- `message_end`: 消息结束
- `agent_message`: Agent模式文本块
- `agent_thought`: Agent思考步骤
- `error`: 错误事件

```python
for event in client.send_message(query="问题", user="user123", response_mode=ResponseMode.STREAMING):
    if event.event == 'message':
        print(event.data.get('answer', ''), end='')
    elif event.event == 'message_end':
        print("\n完成")
        break
    elif event.event == 'error':
        print(f"错误: {event.data}")
        break
```

## 注意事项

1. 请确保API地址和密钥的正确性
2. 流式模式下需要正确处理各种事件类型
3. 建议使用上下文管理器确保资源正确释放
4. 注意API调用频率限制
5. 用户标识需要在应用内保持唯一

## 测试

运行测试文件:

```bash
python test_hanhai_client.py
```

注意：测试前需要在代码中配置正确的API地址和密钥。
